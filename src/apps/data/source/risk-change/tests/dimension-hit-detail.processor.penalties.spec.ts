import { Test, TestingModule } from '@nestjs/testing';
import { DimensionHitDetailProcessor } from '../dimension-hit-detail.processor';
import { BaseHelper } from '../helper/base.helper';
import { DimensionHitStrategyPO } from 'libs/model/diligence/dimension/DimensionHitStrategyPO';
import { HitDetailsBaseQueryParams } from 'libs/model/diligence/details/request';
import { HitDetailsBaseResponse } from 'libs/model/diligence/details/response';
import { DimensionTypeEnums } from 'libs/enums/diligence/DimensionTypeEnums';
import { RiskChangeCategoryEnum } from 'libs/enums/riskchange/RiskChangeCategoryEnum';
import { DimensionDefinitionEntity } from 'libs/entities/DimensionDefinitionEntity';
import { DimensionSourceEnums } from 'libs/enums/diligence/DimensionSourceEnums';
import { PersonHelper } from 'apps/data/helper/person.helper';
import { RiskChangeHelper } from 'apps/data/helper/risk.change.helper';
import { DimensionFieldKeyEnums } from 'libs/enums/dimension/dimension.filter.params';
import { DimensionHitStrategyFieldsEntity } from 'libs/entities/DimensionHitStrategyFieldsEntity';

// 测试辅助函数
const createTestDimensionStrategy = (key: DimensionTypeEnums): DimensionHitStrategyPO => {
  const def = new DimensionDefinitionEntity();
  def.key = key;
  def.name = `测试维度-${key}`;
  def.source = DimensionSourceEnums.RiskChange;
  return new DimensionHitStrategyPO(def);
};

const createTestStrategyField = (fieldKey: DimensionFieldKeyEnums, fieldValue: any): DimensionHitStrategyFieldsEntity => {
  const field = new DimensionHitStrategyFieldsEntity();
  field.dimensionFieldKey = fieldKey;
  field.fieldValue = fieldValue;
  return field;
};

describe('DimensionHitDetailProcessor 处罚类测试', () => {
  let processor: DimensionHitDetailProcessor;
  let mockRiskChangeHelper: jest.Mocked<RiskChangeHelper>;
  let mockPersonHelper: jest.Mocked<PersonHelper>;
  let mockBaseHelper: jest.Mocked<BaseHelper>;
  let mockSearchEs: jest.Mock;

  beforeEach(async () => {
    // 创建 mock 对象
    mockRiskChangeHelper = {
      penaltyUnitField: jest.fn(),
      punishTypeField: jest.fn(),
      checkAmountField: jest.fn(),
      penaltyRedCardFieldCategory107: jest.fn(),
      penaltyIssuingUnitField: jest.fn(),
      checkListedField: jest.fn(),
      businessAbnormalTypeField: jest.fn(),
      punishEnvTypeField: jest.fn(),
      amountField: jest.fn(),
      penaltyRedCardFieldCategory22: jest.fn(),
      penaltyUnitField31: jest.fn(),
      penaltyUnitField117: jest.fn(),
      financialPenaltyCauseTypeField: jest.fn(),
      inspectionResultTypeField: jest.fn(),
    } as any;

    mockPersonHelper = {
      getEmployeeData: jest.fn(),
    } as any;

    mockBaseHelper = {
      filterLastYearData: jest.fn(),
    } as any;

    mockSearchEs = jest.fn();

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        DimensionHitDetailProcessor,
        { provide: RiskChangeHelper, useValue: mockRiskChangeHelper },
        { provide: PersonHelper, useValue: mockPersonHelper },
        { provide: BaseHelper, useValue: mockBaseHelper },
      ],
    }).compile();

    processor = module.get<DimensionHitDetailProcessor>(DimensionHitDetailProcessor);
    processor.bindRiskChangeEsSearchFn(mockSearchEs);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('行政处罚 (category107)', () => {
    it('应该正确处理行政处罚类型的风险动态', async () => {
      // Arrange
      const mockItem = {
        Id: 'test-administrative-penalty',
        Category: RiskChangeCategoryEnum.category107,
        ChangeExtend: JSON.stringify({
          F: '500000', // 处罚金额
          PenaltyUnit: '市场监督管理局',
          PunishType: '罚款',
          IsRedCard: true,
        }),
      };

      const detailResp = new HitDetailsBaseResponse();
      detailResp.Result = [mockItem];

      const dimension = createTestDimensionStrategy(DimensionTypeEnums.RiskChange);
      const penaltyUnitField = createTestStrategyField(DimensionFieldKeyEnums.penaltyUnit, ['市场监督管理局']);
      const punishTypeField = createTestStrategyField(DimensionFieldKeyEnums.punishType, ['罚款']);
      const punishAmountField = createTestStrategyField(DimensionFieldKeyEnums.punishAmount, [30, 100]);
      const punishRedCardField = createTestStrategyField(DimensionFieldKeyEnums.punishRedCard, [true]);
      dimension.strategyFields = [penaltyUnitField, punishTypeField, punishAmountField, punishRedCardField];

      const params = new HitDetailsBaseQueryParams();
      params.keyNo = 'test-company-id';

      mockRiskChangeHelper.penaltyUnitField.mockReturnValue(true);
      mockRiskChangeHelper.punishTypeField.mockReturnValue(true);
      mockRiskChangeHelper.checkAmountField.mockReturnValue(true);
      mockRiskChangeHelper.penaltyRedCardFieldCategory107.mockReturnValue(true);

      // Act
      const result = await processor.fetchHits(detailResp, dimension, params);

      // Assert
      expect(result).toHaveLength(1);
      expect(result[0].Id).toBe('test-administrative-penalty');
      expect(mockRiskChangeHelper.penaltyUnitField).toHaveBeenCalled();
      expect(mockRiskChangeHelper.punishTypeField).toHaveBeenCalled();
      expect(mockRiskChangeHelper.checkAmountField).toHaveBeenCalledWith(
        punishAmountField,
        '500000',
        1
      );
      expect(mockRiskChangeHelper.penaltyRedCardFieldCategory107).toHaveBeenCalled();
    });

    it('应该正确处理带有处罚发布单位和上市公司字段的行政处罚', async () => {
      // Arrange
      const mockItem = {
        Id: 'test-penalty-with-issuing-unit',
        Category: RiskChangeCategoryEnum.category107,
        ChangeExtend: JSON.stringify({
          F: '1000000',
          IssuingUnit: '证监会',
          IsListed: true,
        }),
      };

      const detailResp = new HitDetailsBaseResponse();
      detailResp.Result = [mockItem];

      const dimension = createTestDimensionStrategy(DimensionTypeEnums.RiskChange);
      const penaltyIssuingUnitField = createTestStrategyField(DimensionFieldKeyEnums.penaltyIssuingUnit, ['证监会']);
      const isListedField = createTestStrategyField(DimensionFieldKeyEnums.isListed, [true]);
      dimension.strategyFields = [penaltyIssuingUnitField, isListedField];

      const params = new HitDetailsBaseQueryParams();
      params.keyNo = 'test-company-id';

      mockRiskChangeHelper.penaltyIssuingUnitField.mockReturnValue(true);
      mockRiskChangeHelper.checkListedField.mockResolvedValue(true);

      // Act
      const result = await processor.fetchHits(detailResp, dimension, params);

      // Assert
      expect(result).toHaveLength(1);
      expect(result[0].Id).toBe('test-penalty-with-issuing-unit');
      expect(mockRiskChangeHelper.penaltyIssuingUnitField).toHaveBeenCalled();
      expect(mockRiskChangeHelper.checkListedField).toHaveBeenCalledWith(
        isListedField,
        expect.objectContaining({
          Id: 'test-penalty-with-issuing-unit',
          Category: RiskChangeCategoryEnum.category107,
        }),
        params.keyNo
      );
    });
  });

  describe('经营异常 (category11)', () => {
    it('应该正确处理经营异常类型的风险动态', async () => {
      // Arrange
      const mockItem = {
        Id: 'test-business-abnormal',
        Category: RiskChangeCategoryEnum.category11,
        ChangeExtend: JSON.stringify({
          AbnormalType: '未按规定公示年报',
          AbnormalReason: '连续两年未公示年报',
          ListDate: '2024-01-15',
        }),
      };

      const detailResp = new HitDetailsBaseResponse();
      detailResp.Result = [mockItem];

      const dimension = createTestDimensionStrategy(DimensionTypeEnums.RiskChange);
      const businessAbnormalTypeField = createTestStrategyField(DimensionFieldKeyEnums.businessAbnormalType, ['未按规定公示年报']);
      dimension.strategyFields = [businessAbnormalTypeField];

      const params = new HitDetailsBaseQueryParams();
      params.keyNo = 'test-company-id';

      mockRiskChangeHelper.businessAbnormalTypeField.mockReturnValue(true);

      // Act
      const result = await processor.fetchHits(detailResp, dimension, params);

      // Assert
      expect(result).toHaveLength(1);
      expect(result[0].Id).toBe('test-business-abnormal');
      expect(mockRiskChangeHelper.businessAbnormalTypeField).toHaveBeenCalledWith(
        businessAbnormalTypeField,
        expect.objectContaining({
          Id: 'test-business-abnormal',
          Category: RiskChangeCategoryEnum.category11,
        })
      );
    });
  });

  describe('环保处罚 (category22)', () => {
    it('应该正确处理环保处罚类型的风险动态', async () => {
      // Arrange
      const mockItem = {
        Id: 'test-environmental-penalty',
        Category: RiskChangeCategoryEnum.category22,
        ChangeExtend: JSON.stringify({
          E: '800000', // 处罚金额
          PunishType: '环保罚款',
          IsRedCard: true,
          Reason: '超标排放',
        }),
      };

      const detailResp = new HitDetailsBaseResponse();
      detailResp.Result = [mockItem];

      const dimension = createTestDimensionStrategy(DimensionTypeEnums.RiskChange);
      const punishEnvTypeField = createTestStrategyField(DimensionFieldKeyEnums.punishType, ['环保罚款']);
      const punishAmountField = createTestStrategyField(DimensionFieldKeyEnums.penaltiesAmount, [50, 150]);
      const punishRedCardField = createTestStrategyField(DimensionFieldKeyEnums.punishRedCard, [true]);
      dimension.strategyFields = [punishEnvTypeField, punishAmountField, punishRedCardField];

      const params = new HitDetailsBaseQueryParams();
      params.keyNo = 'test-company-id';

      mockRiskChangeHelper.punishEnvTypeField.mockReturnValue(true);
      mockRiskChangeHelper.amountField.mockReturnValue(true);
      mockRiskChangeHelper.penaltyRedCardFieldCategory22.mockReturnValue(true);

      // Act
      const result = await processor.fetchHits(detailResp, dimension, params);

      // Assert
      expect(result).toHaveLength(1);
      expect(result[0].Id).toBe('test-environmental-penalty');
      expect(mockRiskChangeHelper.punishEnvTypeField).toHaveBeenCalled();
      expect(mockRiskChangeHelper.amountField).toHaveBeenCalledWith(
        punishAmountField,
        '800000',
        1
      );
      expect(mockRiskChangeHelper.penaltyRedCardFieldCategory22).toHaveBeenCalled();
    });
  });

  describe('欠税公告 (category31)', () => {
    it('应该正确处理欠税公告类型的风险动态', async () => {
      // Arrange
      const mockItem = {
        Id: 'test-tax-owed-announcement',
        Category: RiskChangeCategoryEnum.category31,
        ChangeExtend: JSON.stringify({
          B: '1200000', // 欠税金额
          F: '50000', // 处罚金额
          TaxType: '企业所得税',
          PenaltyUnit: '税务局',
        }),
      };

      const detailResp = new HitDetailsBaseResponse();
      detailResp.Result = [mockItem];

      const dimension = createTestDimensionStrategy(DimensionTypeEnums.RiskChange);
      const taxOwedAmountField = createTestStrategyField(DimensionFieldKeyEnums.taxOwedAmount, [80, 200]);
      const penaltyUnitField = createTestStrategyField(DimensionFieldKeyEnums.penaltyUnit, ['税务局']);
      const punishAmountField = createTestStrategyField(DimensionFieldKeyEnums.punishAmount, [3, 10]);
      dimension.strategyFields = [taxOwedAmountField, penaltyUnitField, punishAmountField];

      const params = new HitDetailsBaseQueryParams();
      params.keyNo = 'test-company-id';

      mockRiskChangeHelper.amountField.mockReturnValue(true);
      mockRiskChangeHelper.penaltyUnitField31.mockReturnValue(true);
      mockRiskChangeHelper.checkAmountField.mockReturnValue(true);

      // Act
      const result = await processor.fetchHits(detailResp, dimension, params);

      // Assert
      expect(result).toHaveLength(1);
      expect(result[0].Id).toBe('test-tax-owed-announcement');
      expect(mockRiskChangeHelper.amountField).toHaveBeenCalledWith(
        taxOwedAmountField,
        '1200000',
        10000
      );
      expect(mockRiskChangeHelper.penaltyUnitField31).toHaveBeenCalled();
      expect(mockRiskChangeHelper.checkAmountField).toHaveBeenCalledWith(
        punishAmountField,
        '50000',
        1
      );
    });
  });

  describe('被列入税务非正常户 (category117)', () => {
    it('应该正确处理被列入税务非正常户类型的风险动态', async () => {
      // Arrange
      const mockItem = {
        Id: 'test-tax-abnormal-household',
        Category: RiskChangeCategoryEnum.category117,
        ChangeExtend: JSON.stringify({
          PenaltyUnit: '国家税务总局',
          Reason: '未按期申报纳税',
          ListDate: '2024-02-01',
        }),
      };

      const detailResp = new HitDetailsBaseResponse();
      detailResp.Result = [mockItem];

      const dimension = createTestDimensionStrategy(DimensionTypeEnums.RiskChange);
      const penaltyUnitField = createTestStrategyField(DimensionFieldKeyEnums.penaltyUnit, ['国家税务总局']);
      dimension.strategyFields = [penaltyUnitField];

      const params = new HitDetailsBaseQueryParams();
      params.keyNo = 'test-company-id';

      mockRiskChangeHelper.penaltyUnitField117.mockReturnValue(true);

      // Act
      const result = await processor.fetchHits(detailResp, dimension, params);

      // Assert
      expect(result).toHaveLength(1);
      expect(result[0].Id).toBe('test-tax-abnormal-household');
      expect(mockRiskChangeHelper.penaltyUnitField117).toHaveBeenCalledWith(
        penaltyUnitField,
        expect.objectContaining({
          Id: 'test-tax-abnormal-household',
          Category: RiskChangeCategoryEnum.category117,
        })
      );
    });
  });

  describe('金融监管 (category121)', () => {
    it('应该正确处理金融监管类型的风险动态', async () => {
      // Arrange
      const mockItem = {
        Id: 'test-financial-supervision',
        Category: RiskChangeCategoryEnum.category121,
        ChangeExtend: JSON.stringify({
          PenaltyCause: '违规放贷',
          SupervisorUnit: '银保监会',
          PenaltyAmount: '2000000',
        }),
      };

      const detailResp = new HitDetailsBaseResponse();
      detailResp.Result = [mockItem];

      const dimension = createTestDimensionStrategy(DimensionTypeEnums.RiskChange);
      const financialPenaltyCauseField = createTestStrategyField(DimensionFieldKeyEnums.financialPenaltyCause, ['违规放贷']);
      dimension.strategyFields = [financialPenaltyCauseField];

      const params = new HitDetailsBaseQueryParams();
      params.keyNo = 'test-company-id';

      mockRiskChangeHelper.financialPenaltyCauseTypeField.mockReturnValue(true);

      // Act
      const result = await processor.fetchHits(detailResp, dimension, params);

      // Assert
      expect(result).toHaveLength(1);
      expect(result[0].Id).toBe('test-financial-supervision');
      expect(mockRiskChangeHelper.financialPenaltyCauseTypeField).toHaveBeenCalledWith(
        financialPenaltyCauseField,
        expect.objectContaining({
          Id: 'test-financial-supervision',
          Category: RiskChangeCategoryEnum.category121,
        })
      );
    });
  });

  describe('抽查检查 (category14)', () => {
    it('应该正确处理抽查检查类型的风险动态', async () => {
      // Arrange
      const mockItem = {
        Id: 'test-inspection-check',
        Category: RiskChangeCategoryEnum.category14,
        ChangeExtend: JSON.stringify({
          InspectionResult: '不合格',
          InspectionUnit: '质量监督局',
          InspectionDate: '2024-03-01',
          InspectionType: '产品质量检查',
        }),
      };

      const detailResp = new HitDetailsBaseResponse();
      detailResp.Result = [mockItem];

      const dimension = createTestDimensionStrategy(DimensionTypeEnums.RiskChange);
      const inspectionResultTypeField = createTestStrategyField(DimensionFieldKeyEnums.inspectionResultType, ['不合格']);
      dimension.strategyFields = [inspectionResultTypeField];

      const params = new HitDetailsBaseQueryParams();
      params.keyNo = 'test-company-id';

      mockRiskChangeHelper.inspectionResultTypeField.mockReturnValue(true);

      // Act
      const result = await processor.fetchHits(detailResp, dimension, params);

      // Assert
      expect(result).toHaveLength(1);
      expect(result[0].Id).toBe('test-inspection-check');
      expect(mockRiskChangeHelper.inspectionResultTypeField).toHaveBeenCalledWith(
        inspectionResultTypeField,
        expect.objectContaining({
          Id: 'test-inspection-check',
          Category: RiskChangeCategoryEnum.category14,
        })
      );
    });
  });
});
